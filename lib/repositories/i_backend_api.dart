import 'package:dio/dio.dart';

abstract class IBackendApi {
  Future<Response> addMood(
    String note,
    bool isSharedWithAI,
    double rating,
    String mood,
    String userId,
  );
  Future<void> addJournalEntry(String note, bool isSharedWithAI, String userId);
  Future<Response> sendToAIMessage(String question, String userId);
  Future<Response> getSelfHelpTips();
  Future<Response> postRewriteJournalEntry(String text);
}

import 'package:dio/dio.dart';

import '../config/app_keys.dart';
import '../config/server_config.dart';
import 'i_audio_server_api.dart';

class AudioServerApi implements IAudioServerApi {
  final Dio _dio = Dio();
  final String _baseUrl = ServerConfig.serverUrlAudio;
  final String _bearerToken = AppKeys.backendApiAudioKey;

  @override
  Future<Response> getAudioUrl(String category) async {
    final response = await _dio.get(
      '$_baseUrl/audio-files?category=$category',
      options: Options(
        headers: {
          'Authorization': 'Bearer $_bearerToken',
          'Content-Type': 'application/json; charset=utf-8',
        },
        contentType: 'application/json',
      ),
    );

    return response;
  }
}

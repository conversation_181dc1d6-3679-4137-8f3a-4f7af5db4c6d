import '../models/activities_model.dart';
import '../objectbox.g.dart';
import 'i_add_activities_local_db.dart';

class AddActivitiesLocalDb implements IAddActivitiesLocalDb {
  final Box<ActivitiesModel> _box;

  AddActivitiesLocalDb(this._box);

  @override
  Future<void> saveActivity(ActivitiesModel model) async {
    _box.put(model);
  }

  @override
  Future<List<ActivitiesModel>> getActivities() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteActivity(int id) async {
    _box.remove(id);
  }

  @override
  Future<void> deleteAllActivities() async {
    _box.removeAll();
  }
}

import 'package:go_router/go_router.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/main_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/start_image_screen.dart';
import '../screens/sub_home_screens/ai_info_screen.dart';
import '../screens/sub_home_screens/audio_player_screen.dart';
import '../screens/sub_home_screens/book_therapist_screen.dart';
import '../screens/sub_home_screens/notifications_screen.dart';
import '../screens/sub_home_screens/reports_screen.dart';
import '../screens/sub_home_screens/relax_unwind_screen.dart';
import '../screens/sub_home_screens/self_help_tips_detail.dart';
import '../screens/sub_journal/add_journal_entry.dart';
import '../screens/sub_mood_screen/add_mood_screen.dart';
import '../screens/sub_mood_screen/calendar_view.dart';
import 'analytics_route_observer.dart';

final router = GoRouter(
  initialLocation: '/',
  observers: [AnalyticsRouteObserver()],
  routes: [
    GoRoute(path: '/', builder: (context, state) => const StartImageScreen()),
    GoRoute(
      path: '/mainScreen',
      builder: (context, state) => const MainScreen(),
    ),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
    GoRoute(
      path: '/addMood',
      builder:
          (context, state) =>
              AddMoodScreen(dateParam: state.uri.queryParameters['date']),
    ),
    GoRoute(
      path: '/settings',
      builder: (context, state) => const SettingsScreen(),
    ),
    GoRoute(
      path: '/addJournalEntry',
      builder: (context, state) => const AddJournalEntry(),
    ),
    GoRoute(
      path: '/bookTherapist',
      builder: (context, state) => const BookTherapistScreen(),
    ),
    GoRoute(
      path: '/calendarView',
      builder: (context, state) => const CalendarView(),
    ),
    GoRoute(
      path: '/selfHelpTips',
      builder: (context, state) => const RelaxUnwindScreen(),
    ),
    GoRoute(
      path: '/notifications',
      builder: (context, state) => const NotificationsScreen(),
    ),
    GoRoute(
      path: '/reports',
      builder: (context, state) => const ReportsScreen(),
    ),
    GoRoute(
      path: '/selfHelpTipsDetail/:id',
      builder: (context, state) {
        final id = state.pathParameters['id'];
        return SelfHelpTipsDetailScreen(screen: id);
      },
    ),
    GoRoute(path: '/aiInfo', builder: (context, state) => const AIInfoScreen()),

    GoRoute(
      path: '/audioPlayer',
      builder: (context, state) {
        final audioUrl = state.uri.queryParameters['audioUrl'];
        final title = state.uri.queryParameters['title'];
        return AudioPlayerScreen(audioUrl: audioUrl ?? '', title: title ?? '');
      },
    ),
  ],
);

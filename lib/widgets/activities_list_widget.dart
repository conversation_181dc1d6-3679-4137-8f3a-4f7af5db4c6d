import 'package:flutter/material.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class ActivitiesListWidget extends StatelessWidget {
  final List<MoodModel> moods;
  final int daysToShow;

  const ActivitiesListWidget({
    super.key,
    required this.moods,
    this.daysToShow = 10,
  });

  @override
  Widget build(BuildContext context) {
    final activityData = _calculateActivityData();

    if (activityData.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.citrus.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.list_alt_rounded,
                    color: AppTheme.blueberry,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Activity Types',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.blueberry,
                        ),
                      ),
                      Text(
                        'Last $daysToShow days',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.blueberry.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.apricot.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${activityData.length} types',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.apricot,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppTheme.citrus.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            itemCount: activityData.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final activity = activityData[index];
              final isTopActivity = index == 0;

              return Container(
                decoration: BoxDecoration(
                  color:
                      isTopActivity
                          ? AppTheme.citrus.withValues(alpha: 0.15)
                          : activity.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        isTopActivity
                            ? AppTheme.citrus.withValues(alpha: 0.3)
                            : activity.color.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Rank badge
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color:
                              isTopActivity
                                  ? AppTheme.apricot
                                  : AppTheme.blueberry.withValues(alpha: 0.8),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Activity info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              activity.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.blueberry,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              activity.count == 1
                                  ? '1 time'
                                  : '${activity.count} times',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.blueberry.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Count badge
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: activity.color.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${activity.count}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: activity.color,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.citrus.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.list_alt_rounded,
                size: 48,
                color: AppTheme.blueberry.withValues(alpha: 0.5),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'No Activities Yet',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start adding activities to your moods to see them listed here!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.blueberry.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ActivityListData> _calculateActivityData() {
    final Map<String, int> activityCounts = {};
    final now = DateTime.now();
    final cutoffDate = DateTime(now.year, now.month, now.day - daysToShow);

    // Count activities from moods within the specified time period
    for (final mood in moods) {
      final moodDate = mood.createdAt ?? DateTime.now();

      // Only include moods from the last N days
      if (moodDate.isAfter(cutoffDate) &&
          mood.activity != null &&
          mood.activity!.isNotEmpty) {
        activityCounts[mood.activity!] =
            (activityCounts[mood.activity!] ?? 0) + 1;
      }
    }

    // Convert to list and sort by count (descending)
    final activityList =
        activityCounts.entries
            .map(
              (entry) => ActivityListData(
                name: entry.key,
                count: entry.value,
                color: _getActivityColor(entry.key),
              ),
            )
            .toList();

    activityList.sort((a, b) => b.count.compareTo(a.count));
    return activityList;
  }

  Color _getActivityColor(String activityName) {
    // Generate consistent colors for activities
    final colors = [
      AppTheme.apricot,
      AppTheme.citrus,
      AppTheme.blueberry,
      const Color(0xFF8E44AD), // Purple
      const Color(0xFF27AE60), // Green
      const Color(0xFFE74C3C), // Red
      const Color(0xFF3498DB), // Blue
      const Color(0xFFF39C12), // Orange
      const Color(0xFF9B59B6), // Violet
      const Color(0xFF1ABC9C), // Turquoise
    ];

    final index = activityName.hashCode.abs() % colors.length;
    return colors[index];
  }
}

class ActivityListData {
  final String name;
  final int count;
  final Color color;

  ActivityListData({
    required this.name,
    required this.count,
    required this.color,
  });
}

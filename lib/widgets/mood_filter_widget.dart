import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/mood_controller.dart';
import '../theme/app_theme.dart';

class MoodFilterWidget extends WatchingStatefulWidget {
  const MoodFilterWidget({super.key});

  @override
  State<MoodFilterWidget> createState() => _MoodFilterWidgetState();
}

class _MoodFilterWidgetState extends State<MoodFilterWidget> {
  final List<String> moodOptions = ['Mad', 'Bad', 'OK', 'Happy', 'Joy'];
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final moodController = watchIt<MoodController>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.citrus.withAlpha(25),
            AppTheme.appleCore.withA<PERSON>pha(13),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.blueberry.withAlpha(51), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Icon(Icons.filter_list, color: AppTheme.blueberry, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Filter Moods',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.blueberry,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppTheme.blueberry,
                    size: 20,
                  ),
                  const Spacer(),
                  if (moodController.selectedMoodFilter != null ||
                      moodController.selectedActivityFilter != null)
                    TextButton(
                      onPressed: () {
                        di<MoodController>().clearFilters();
                      },
                      child: Text(
                        'Clear',
                        style: TextStyle(
                          color: AppTheme.apricot,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Expandable content
          if (_isExpanded) ...[
            const SizedBox(height: 12),

            // Mood Filter
            Text(
              'By Mood:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.blueberry,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  moodOptions.map((mood) {
                    final isSelected =
                        moodController.selectedMoodFilter == mood;
                    return FilterChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            'assets/images/${mood.toLowerCase()}.png',
                            width: 16,
                            height: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(mood),
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          di<MoodController>().sortFilterMoodsByMoodOrActivity(
                            moodFilter: mood,
                            activityFilter:
                                moodController.selectedActivityFilter,
                          );
                        } else {
                          di<MoodController>().sortFilterMoodsByMoodOrActivity(
                            moodFilter: null,
                            activityFilter:
                                moodController.selectedActivityFilter,
                          );
                        }
                      },
                      selectedColor: AppTheme.citrus.withAlpha(77),
                      checkmarkColor: AppTheme.blueberry,
                      backgroundColor: Colors.white.withAlpha(128),
                      side: BorderSide(
                        color:
                            isSelected
                                ? AppTheme.citrus
                                : AppTheme.blueberry.withAlpha(77),
                      ),
                    );
                  }).toList(),
            ),

            const SizedBox(height: 16),

            // Activity Filter
            Text(
              'By Activity:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.blueberry,
              ),
            ),
            const SizedBox(height: 8),

            // Activity Dropdown
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(128),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      moodController.selectedActivityFilter != null
                          ? AppTheme.citrus
                          : AppTheme.blueberry.withAlpha(77),
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: moodController.selectedActivityFilter,
                  hint: Text(
                    'Select Activity',
                    style: TextStyle(color: AppTheme.blueberry.withAlpha(179)),
                  ),
                  isExpanded: true,
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Activities'),
                    ),
                    ...moodController.activities.map((activity) {
                      return DropdownMenuItem<String>(
                        value: activity.activityName,
                        child: Text(activity.activityName),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    di<MoodController>().sortFilterMoodsByMoodOrActivity(
                      moodFilter: moodController.selectedMoodFilter,
                      activityFilter: value,
                    );
                  },
                  dropdownColor: Colors.white,
                  style: TextStyle(color: AppTheme.blueberry, fontSize: 18),
                  icon: Icon(Icons.arrow_drop_down, color: AppTheme.blueberry),
                ),
              ),
            ),
          ], // End of conditional content
        ],
      ),
    );
  }
}

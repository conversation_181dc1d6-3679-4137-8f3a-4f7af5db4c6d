import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:watch_it/watch_it.dart';
import 'controllers/theme_mode_controller.dart';
import 'firebase_options.dart';
import 'globals.dart';
import 'local_db/init_object_box.dart';
import 'router/rounter.dart';
import 'store/constant.dart';
import 'store/store_config.dart';
import 'theme/app_theme.dart';
import 'utilities/load_data_only_one.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  await initObjectBox();

  await di<ThemeModeController>().initialize();

  await loadActivitiesOnce();

  if (Platform.isIOS || Platform.isMacOS) {
    StoreConfig(store: MyStore.appleStore, apiKey: appleApiKey);
  } else if (Platform.isAndroid) {
    StoreConfig(store: MyStore.googlePlay, apiKey: googleApiKey);
  }

  await _configureSDK();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget with WatchItMixin {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = watchIt<ThemeModeController>();
    return MaterialApp.router(
      builder: (context, child) {
        final MediaQueryData data = MediaQuery.of(context);
        return MediaQuery(
          data: data.copyWith(textScaler: TextScaler.noScaling),
          child: child!,
        );
      },
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: theme.mode == "dark" ? ThemeMode.dark : ThemeMode.light,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      scaffoldMessengerKey: snackbarKey,
    );
  }
}

Future<void> _configureSDK() async {
  await Purchases.setLogLevel(LogLevel.error);

  PurchasesConfiguration configuration;
  {
    configuration = PurchasesConfiguration(StoreConfig.instance.apiKey)
      ..appUserID = null;
  }
  await Purchases.configure(configuration);
}

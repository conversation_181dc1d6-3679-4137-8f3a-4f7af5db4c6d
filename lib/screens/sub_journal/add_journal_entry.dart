import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:watch_it/watch_it.dart';
import 'package:path_provider/path_provider.dart';

import '../../controllers/journal_contoller.dart';
import '../../theme/app_theme.dart';
import '../../theme/text_sizes.dart';
import '../../utilities/snack_bar_show.dart';

class AddJournalEntry extends StatefulWidget {
  const AddJournalEntry({super.key});

  @override
  State<AddJournalEntry> createState() => _AddJournalEntryState();
}

class _AddJournalEntryState extends State<AddJournalEntry> {
  final _formKey = GlobalKey<FormState>();

  bool isSharedWithAI = false;

  final noteController = TextEditingController();

  // Image picker
  final ImagePicker _imagePicker = ImagePicker();
  XFile? image;
  String? imagePathPermanent;

  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  bool _isListening = false;
  bool _speechCooldown = false; // Prevent rapid button presses
  bool _isRewriting = false; // Prevent rapid rewrite button presses
  String _lastWords = '';

  // Key to force TextFormField rebuild
  Key _textFieldKey = UniqueKey();

  @override
  void initState() {
    noteController.clear();
    _initSpeech();
    super.initState();
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(title: const Text('Add Journal Entry'), elevation: 0),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          children: [
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: TextFormField(
                key: _textFieldKey,
                style: TextStyle(fontSize: TextSizes.textFieldText),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter some text';
                  }
                  return null;
                },
                controller: noteController,
                maxLines: 4,
                maxLength: 500,
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    onPressed:
                        (_speechEnabled && !_speechCooldown)
                            ? speechToText
                            : null,
                    icon: Icon(
                      _isListening ? Icons.mic : Icons.mic_none,
                      size: 30,
                      color:
                          _isListening
                              ? Colors.red
                              : (_speechEnabled
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey),
                    ),
                  ),
                  labelText: 'Note',
                  labelStyle: TextStyle(fontSize: TextSizes.textFieldText),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.only(left: 16.0, right: 16.0),
              child: Row(
                children: [
                  Text(
                    "AI Tools:",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.blueberry,
                    ),
                  ),
                  const SizedBox(width: 12),
                  InkWell(
                    onTap: () async {
                      if (noteController.text.isEmpty) {
                        _showErrorSnackBar('Please enter some text to rewrite');
                        return;
                      }

                      // Prevent rapid button presses
                      if (_isRewriting) {
                        return;
                      }

                      // Show loading indicator
                      setState(() {
                        _isRewriting = true;
                      });

                      try {
                        final rewrittenText = await di<JournalController>()
                            .rewriteJournalEntry(noteController.text.trim());

                        if (mounted && rewrittenText.isNotEmpty) {
                          // Update the text field with rewritten content
                          setState(() {
                            // Clear the controller first
                            noteController.clear();
                            _isRewriting = false;
                            // Generate new key to force rebuild
                            _textFieldKey = UniqueKey();
                          });

                          // Add a small delay then set the new text
                          await Future.delayed(Duration(milliseconds: 50));

                          if (mounted) {
                            setState(() {
                              noteController.text = rewrittenText;
                              // Force cursor to end of text
                              noteController
                                  .selection = TextSelection.fromPosition(
                                TextPosition(
                                  offset: noteController.text.length,
                                ),
                              );
                            });
                          }

                          // Show success message
                          if (mounted) {
                            di<SnackBarShow>().showSnackBar(
                              'Journal entry rewritten successfully!',
                              AppTheme.blueberry,
                            );
                          }
                        } else {
                          if (mounted) {
                            setState(() {
                              _isRewriting = false;
                            });
                          }
                        }
                      } catch (e) {
                        if (mounted) {
                          setState(() {
                            _isRewriting = false;
                          });
                        }
                        debugPrint('Error in rewrite UI: $e');
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors:
                              _isRewriting
                                  ? [
                                    AppTheme.apricot.withValues(alpha: 0.05),
                                    AppTheme.citrus.withValues(alpha: 0.02),
                                  ]
                                  : [
                                    AppTheme.apricot.withValues(alpha: 0.1),
                                    AppTheme.citrus.withValues(alpha: 0.05),
                                  ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color:
                              _isRewriting
                                  ? AppTheme.apricot.withValues(alpha: 0.15)
                                  : AppTheme.apricot.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.edit_outlined,
                            color: AppTheme.apricot,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          _isRewriting
                              ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.apricot,
                                  ),
                                ),
                              )
                              : Text(
                                "Rewrite",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color:
                                      _isRewriting
                                          ? AppTheme.blueberry.withValues(
                                            alpha: 0.5,
                                          )
                                          : AppTheme.blueberry,
                                ),
                              ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16.0),
            //   child: Row(
            //     children: [
            //       Text("Share with AI", style: TextStyle(fontSize: 18)),
            //       SizedBox(width: 10),
            //       Switch(
            //         value: isSharedWithAI,
            //         onChanged: (value) {
            //           setState(() {
            //             isSharedWithAI = value;
            //           });
            //         },
            //       ),
            //       const SizedBox(width: 10),
            //       IconButton(
            //         onPressed: () {
            //           context.push('/aiInfo');
            //         },
            //         icon: Icon(Icons.info_outline, color: Colors.grey),
            //       ),
            //       const SizedBox(width: 5),
            //     ],
            //   ),
            // ),
            const SizedBox(height: 20),
            // Image picker section
            if (imagePathPermanent != null) ...[
              // Show selected image preview
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: Image.file(
                    File(imagePathPermanent!),
                    fit: BoxFit.cover,
                    width: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 50,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton.icon(
                    onPressed: pickImage,
                    icon: const Icon(Icons.edit),
                    label: const Text('Change Image'),
                  ),
                  const SizedBox(width: 16),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        image = null;
                        imagePathPermanent = null;
                      });
                    },
                    icon: const Icon(Icons.delete, color: Colors.red),
                    label: const Text(
                      'Remove',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // Show image picker button
              IconButton(
                onPressed: pickImage,
                icon: Icon(
                  Icons.add_photo_alternate,
                  color: Theme.of(context).colorScheme.primary,
                  size: MediaQuery.of(context).size.width / 4,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap to add an image',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton(
                onPressed: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (_formKey.currentState!.validate()) {
                    // Save journal entry with permanent image path
                    di<JournalController>().addJournalEntry(
                      noteController.text.trim(),
                      isSharedWithAI,
                      image,
                      imagePathPermanent,
                    );
                  }
                },
                child: Text(
                  "Save",
                  style: TextStyle(fontSize: TextSizes.buttonText),
                ),
              ),
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Future<void> pickImage() async {
    try {
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );

      if (pickedImage != null) {
        // Get app's permanent directory
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final Directory imageDir = Directory(
          '${appDocDir.path}/journal_images',
        );

        // Create directory if it doesn't exist
        if (!await imageDir.exists()) {
          await imageDir.create(recursive: true);
        }

        // Get original file extension
        final String originalExtension = pickedImage.path.split('.').last;
        final String fileName =
            '${DateTime.now().millisecondsSinceEpoch}.$originalExtension';
        final File permanentFile = File('${imageDir.path}/$fileName');

        // Copy the temporary file to permanent app storage
        await File(pickedImage.path).copy(permanentFile.path);

        setState(() {
          image = pickedImage;
          imagePathPermanent = permanentFile.path;
        });

        debugPrint('Image saved permanently at: $imagePathPermanent');
      }
    } catch (e) {
      debugPrint('Error saving image: $e');
      _showErrorSnackBar('Failed to save image');
    }
  }

  /// Initialize speech-to-text
  Future<void> _initSpeech() async {
    _speechEnabled = await _speechToText.initialize(
      onStatus: (status) {
        debugPrint('Speech status: $status');
        if (status == 'done' || status == 'notListening') {
          setState(() {
            _isListening = false;
          });
        }
      },
      onError: (error) {
        debugPrint('Speech error: $error');
        setState(() {
          _isListening = false;
        });

        // Handle different types of speech errors with user-friendly messages
        String errorMessage;
        debugPrint(
          'Speech error type: "${error.errorMsg}"',
        ); // Debug the exact error

        if (error.errorMsg.contains('timeout')) {
          errorMessage = 'No speech detected - please try speaking again';
        } else if (error.errorMsg.contains('no_match') ||
            error.errorMsg.contains('no match')) {
          errorMessage = 'Could not understand speech - please try again';
        } else if (error.errorMsg.contains('busy')) {
          errorMessage =
              'Speech recognition is busy - please wait and try again';
        } else if (error.errorMsg.contains('permission')) {
          errorMessage =
              'Microphone permission required for speech recognition';
        } else if (error.errorMsg.contains('network')) {
          errorMessage = 'Network error - check your internet connection';
        } else {
          // For any other errors, show a generic friendly message
          errorMessage =
              'Speech recognition unavailable - please try typing instead';
        }

        _showErrorSnackBar(errorMessage);
      },
    );
    setState(() {});
    debugPrint('Speech enabled: $_speechEnabled');
  }

  /// Start/stop speech recognition
  void speechToText() async {
    if (!_speechEnabled) {
      _showErrorSnackBar('Speech recognition not available');
      return;
    }

    // Check cooldown to prevent rapid button presses
    if (_speechCooldown) {
      return;
    }

    // Set cooldown
    setState(() {
      _speechCooldown = true;
    });

    // Reset cooldown after 1 second
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _speechCooldown = false;
        });
      }
    });

    if (_isListening) {
      // Stop listening
      await _speechToText.stop();
      setState(() {
        _isListening = false;
      });
    } else {
      // Start listening
      setState(() {
        _isListening = true;
        _lastWords = '';
      });

      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _lastWords = result.recognizedWords;
            if (result.finalResult) {
              // Append the recognized text to the existing text
              String currentText = noteController.text;
              String newText =
                  currentText.isEmpty ? _lastWords : '$currentText $_lastWords';

              // Ensure we don't exceed the max length
              if (newText.length <= 500) {
                noteController.text = newText;
                noteController.selection = TextSelection.fromPosition(
                  TextPosition(offset: noteController.text.length),
                );
              } else {
                _showErrorSnackBar('Text limit reached (500 characters)');
              }

              _isListening = false;
            }
          });
        },
        listenFor: const Duration(seconds: 60), // Extended listening time
        pauseFor: const Duration(seconds: 5), // Longer pause tolerance
        localeId: 'en_US',
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: true,
          listenMode: ListenMode.confirmation,
        ),
      );
    }
  }

  /// Show error message to user
  void _showErrorSnackBar(String message) {
    debugPrint(message);
    if (mounted) {
      di<SnackBarShow>().showSnackBar(message, AppTheme.blueberry);
    }
  }
}

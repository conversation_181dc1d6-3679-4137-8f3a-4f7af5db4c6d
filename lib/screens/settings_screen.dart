import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/settings_controller.dart';
import '../controllers/theme_mode_controller.dart';
import '../theme/app_theme.dart';

class SettingsScreen extends StatelessWidget with WatchItMixin {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = watchIt<ThemeModeController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Settings'), elevation: 0),
      body: ListView(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 8),
        children: [
          Card(
            child: ListTile(
              title: const Text(
                'Light/Dark Mode',
                style: TextStyle(fontSize: 20),
              ),
              subtitle: Text(
                'Theme : ${theme.mode}',
                style: TextStyle(fontSize: 18),
              ),
              trailing: Icon(size: 30, Icons.dark_mode),
              onTap: () async {
                debugPrint("Settings: Toggling theme mode");
                await di<ThemeModeController>().toggleMode();
                debugPrint("Settings: Theme mode toggled");
              },
            ),
          ),
          Card(
            child: ListTile(
              title: const Text('Our website', style: TextStyle(fontSize: 20)),

              trailing: Icon(size: 30, Icons.web),
              onTap: () {
                _launchUrl(Uri.parse('https://moodvibe.org'));
              },
            ),
          ),
          const SizedBox(height: 10),
          Card(
            child: ListTile(
              title: const Text(
                'Notifications',
                style: TextStyle(fontSize: 20),
              ),
              trailing: Icon(size: 30, Icons.notifications),
              onTap: () async {
                context.push('/notifications');
              },
            ),
          ),
          SizedBox(height: 10),
          Card(
            child: ListTile(
              title: const Text(
                'Tell your friends',
                style: TextStyle(fontSize: 20),
              ),
              trailing: Icon(size: 30, Icons.handshake),
              onTap: () {
                SharePlus.instance.share(
                  ShareParams(
                    text:
                        'I found this great app called MoodVibe. Check it out at https://moodvibe.org',
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 10),
          Card(
            child: ListTile(
              title: const Text(
                'Delete all data',
                style: TextStyle(fontSize: 20),
              ),
              subtitle: Text(
                'All data will be deleted.',
                style: TextStyle(fontSize: 18),
              ),
              trailing: Icon(
                size: 30,
                Icons.delete,
                color: Theme.of(context).colorScheme.error,
              ),
              onTap: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: const Text('Confirm Deletion'),
                      content: const Text(
                        'Are you sure you want to delete all data?',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(); // Close the dialog
                          },
                          child: const Text(
                            'No',
                            style: TextStyle(color: Colors.green, fontSize: 20),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(); // Close the dialog
                            di<SettingsController>().deleteAllData();
                          },
                          child: const Text(
                            'Yes',
                            style: TextStyle(color: Colors.red, fontSize: 20),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchUrl(url) async {
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}

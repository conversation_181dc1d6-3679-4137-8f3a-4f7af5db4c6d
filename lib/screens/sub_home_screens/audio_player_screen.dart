import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class AudioPlayerScreen extends StatefulWidget {
  final String audioUrl;
  final String title;

  const AudioPlayerScreen({
    super.key,
    required this.audioUrl,
    required this.title,
  });

  @override
  State<AudioPlayerScreen> createState() => _AudioPlayerScreenState();
}

class _AudioPlayerScreenState extends State<AudioPlayerScreen> {
  late AudioPlayer player;
  bool isPlaying = false;
  Duration duration = Duration.zero;
  Duration position = Duration.zero;

  // Stream subscriptions to properly dispose of listeners
  late StreamSubscription<PlayerState> _playerStateSubscription;
  late StreamSubscription<Duration> _durationSubscription;
  late StreamSubscription<Duration> _positionSubscription;

  // Download state
  bool isDownloading = false;
  bool isDownloaded = false;
  double downloadProgress = 0.0;
  String? localFilePath;
  String? downloadError;

  @override
  void initState() {
    super.initState();
    player = AudioPlayer();

    // Listen to player state changes
    _playerStateSubscription = player.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          isPlaying = state == PlayerState.playing;
        });
      }
    });

    // Listen to audio duration changes
    _durationSubscription = player.onDurationChanged.listen((newDuration) {
      if (mounted) {
        setState(() {
          duration = newDuration;
        });
      }
    });

    // Listen to audio position changes
    _positionSubscription = player.onPositionChanged.listen((newPosition) {
      if (mounted) {
        setState(() {
          position = newPosition;
        });
      }
    });

    // Start downloading the audio file
    _downloadAudioFile();
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions
    _playerStateSubscription.cancel();
    _durationSubscription.cancel();
    _positionSubscription.cancel();

    // Dispose of the player
    player.dispose();

    // Clean up downloaded file
    _cleanupDownloadedFile();

    super.dispose();
  }

  Future<void> _downloadAudioFile() async {
    if (!mounted) return;

    setState(() {
      isDownloading = true;
      downloadProgress = 0.0;
      downloadError = null;
    });

    try {
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();

      // Create a unique filename based on the URL
      final fileName = 'audio_${widget.audioUrl.hashCode.abs()}.mp3';
      final filePath = path.join(tempDir.path, fileName);

      // Check if file already exists
      final file = File(filePath);
      if (await file.exists()) {
        debugPrint('✅ Audio file already exists locally: $filePath');
        if (mounted) {
          setState(() {
            localFilePath = filePath;
            isDownloaded = true;
            isDownloading = false;
          });
        }
        return;
      }

      debugPrint('📥 Starting download: ${widget.audioUrl}');
      debugPrint('💾 Saving to: $filePath');

      // Download the file
      final dio = Dio();
      await dio.download(
        widget.audioUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total > 0 && mounted) {
            final progress = received / total;
            setState(() {
              downloadProgress = progress;
            });
            debugPrint(
              '📊 Download progress: ${(progress * 100).toStringAsFixed(1)}%',
            );
          }
        },
      );

      debugPrint('✅ Download completed successfully');

      if (mounted) {
        setState(() {
          localFilePath = filePath;
          isDownloaded = true;
          isDownloading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Download failed: $e');
      if (mounted) {
        setState(() {
          downloadError = 'Failed to download audio: $e';
          isDownloading = false;
        });
      }
    }
  }

  Future<void> _cleanupDownloadedFile() async {
    if (localFilePath != null) {
      try {
        final file = File(localFilePath!);
        if (await file.exists()) {
          await file.delete();
          debugPrint('🗑️ Cleaned up downloaded file: $localFilePath');
        }
      } catch (e) {
        debugPrint('⚠️ Failed to cleanup file: $e');
      }
    }
  }

  String formatTime(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(
        title: Text('Audio Player'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 40),

              // Main player card
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.apricot.withValues(alpha: 0.1),
                        AppTheme.citrus.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: AppTheme.blueberry.withValues(alpha: 0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.blueberry.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Audio icon
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: AppTheme.citrus.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppTheme.citrus.withValues(alpha: 0.4),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Icons.headphones_rounded,
                            size: 64,
                            color: AppTheme.blueberry,
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Audio title
                        Text(
                          widget.title,
                          textAlign: TextAlign.center,
                          style: Theme.of(
                            context,
                          ).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.blueberry,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Download status
                        if (isDownloading)
                          Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.citrus.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppTheme.citrus.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              AppTheme.blueberry,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Downloading... ${(downloadProgress * 100).toStringAsFixed(0)}%',
                                      style: TextStyle(
                                        color: AppTheme.blueberry,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: 200,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: AppTheme.blueberry.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                child: FractionallySizedBox(
                                  alignment: Alignment.centerLeft,
                                  widthFactor: downloadProgress,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppTheme.blueberry,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        else if (downloadError != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.red.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 16,
                                  color: Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Download failed',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          )
                        else if (isDownloaded)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.green.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 16,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Ready to play',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 32),

                        // Progress slider
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: SliderTheme(
                            data: SliderTheme.of(context).copyWith(
                              activeTrackColor: AppTheme.blueberry,
                              inactiveTrackColor: AppTheme.blueberry.withValues(
                                alpha: 0.2,
                              ),
                              thumbColor: AppTheme.blueberry,
                              overlayColor: AppTheme.blueberry.withValues(
                                alpha: 0.2,
                              ),
                              trackHeight: 4,
                              thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 8,
                              ),
                            ),
                            child: Slider(
                              min: 0,
                              max:
                                  duration.inSeconds > 0
                                      ? duration.inSeconds.toDouble()
                                      : 1.0,
                              value:
                                  duration.inSeconds > 0
                                      ? position.inSeconds.toDouble().clamp(
                                        0.0,
                                        duration.inSeconds.toDouble(),
                                      )
                                      : 0.0,
                              onChanged:
                                  duration.inSeconds > 0
                                      ? (value) async {
                                        final position = Duration(
                                          seconds: value.toInt(),
                                        );
                                        await player.seek(position);
                                      }
                                      : null,
                            ),
                          ),
                        ),

                        // Time display
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                formatTime(position),
                                style: TextStyle(
                                  color: AppTheme.blueberry.withValues(
                                    alpha: 0.8,
                                  ),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                duration.inSeconds > 0
                                    ? formatTime(duration - position)
                                    : '--:--',
                                style: TextStyle(
                                  color: AppTheme.blueberry.withValues(
                                    alpha: 0.8,
                                  ),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 48),

                        // Control buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Play/Pause button
                            Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppTheme.blueberry,
                                    AppTheme.blueberry.withValues(alpha: 0.8),
                                  ],
                                ),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: AppTheme.blueberry.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(40),
                                  onTap:
                                      isDownloading
                                          ? null
                                          : () async {
                                            if (isPlaying) {
                                              await player.pause();
                                            } else {
                                              // Use local file if downloaded, otherwise fallback to URL
                                              if (isDownloaded &&
                                                  localFilePath != null) {
                                                await player.play(
                                                  DeviceFileSource(
                                                    localFilePath!,
                                                  ),
                                                );
                                              } else {
                                                await player.play(
                                                  UrlSource(widget.audioUrl),
                                                );
                                              }
                                            }
                                          },
                                  child: SizedBox(
                                    width: 80,
                                    height: 80,
                                    child:
                                        isDownloading
                                            ? SizedBox(
                                              width: 40,
                                              height: 40,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 3,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                      Color
                                                    >(Colors.white),
                                              ),
                                            )
                                            : Icon(
                                              isPlaying
                                                  ? Icons.pause_rounded
                                                  : Icons.play_arrow_rounded,
                                              size: 40,
                                              color: Colors.white,
                                            ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 24),
                            // Stop button
                            Container(
                              decoration: BoxDecoration(
                                color: AppTheme.apricot.withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: AppTheme.apricot.withValues(
                                    alpha: 0.3,
                                  ),
                                  width: 2,
                                ),
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(35),
                                  onTap: () async {
                                    await player.stop();
                                  },
                                  child: SizedBox(
                                    width: 70,
                                    height: 70,
                                    child: Icon(
                                      Icons.stop_rounded,
                                      size: 32,
                                      color: AppTheme.apricot,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/mood_controller.dart';
import '../../models/mood.dart';
import '../../theme/icon_size.dart';
import '../../widgets/mood_card_widget.dart';

class MoodScreen extends WatchingStatefulWidget {
  const MoodScreen({super.key});

  @override
  State<MoodScreen> createState() => _MoodScreenState();
}

class _MoodScreenState extends State<MoodScreen> {
  @override
  Widget build(BuildContext context) {
    final moodController = watchIt<MoodController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(
        title: const Text('Moods'),
        elevation: 0,
        actions: [
          // IconButton(
          //   onPressed: () {
          //     context.push('/reports');
          //   },
          //   icon: const Icon(Icons.bar_chart, size: AppIconSize.appIconSize),
          // ),
          IconButton(
            onPressed: () {
              context.push('/calendarView');
            },
            icon: const Icon(
              Icons.calendar_month,
              size: AppIconSize.appIconSize,
            ),
          ),
          IconButton(
            onPressed: () {
              context.push('/settings');
            },
            icon: const Icon(Icons.settings, size: AppIconSize.appIconSize),
          ),
        ],
      ),
      body:
          moodController.isLoading
              ? const Center(child: CircularProgressIndicator())
              : moodController.moods.isEmpty
              ? _buildEmptyState(context)
              : _buildGroupedMoodList(context, moodController.moods),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/addMood');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mood,
            size: 80,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No mood entries yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to add your first mood',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupedMoodList(BuildContext context, List<MoodModel> moods) {
    // Group moods by day
    final groupedMoods = _groupMoodsByDay(moods);

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 80),
      itemCount: groupedMoods.length,
      itemBuilder: (context, index) {
        final dayGroup = groupedMoods[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDayHeader(context, dayGroup.date, dayGroup.moods.length),
            ...dayGroup.moods.asMap().entries.map((entry) {
              final moodIndex = entry.key;
              final mood = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: MoodCardWidget(
                  mood: mood,
                  showDismissible: true,
                  index: moodIndex,
                ),
              );
            }),
            const SizedBox(height: 8), // Space between day groups
          ],
        );
      },
    );
  }

  Widget _buildDayHeader(BuildContext context, DateTime date, int moodCount) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final moodDate = DateTime(date.year, date.month, date.day);

    String dayLabel;
    if (moodDate == today) {
      dayLabel = 'Today';
    } else if (moodDate == yesterday) {
      dayLabel = 'Yesterday';
    } else {
      dayLabel = DateFormat('EEEE, MMMM d').format(date);
    }

    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 12, left: 8, right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.citrus.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.citrus.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.calendar_today_rounded,
              color: AppTheme.blueberry,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              dayLabel,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppTheme.apricot.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              moodCount == 1 ? '1 mood' : '$moodCount moods',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.apricot,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DayMoodGroup> _groupMoodsByDay(List<MoodModel> moods) {
    // Sort moods by date (newest first)
    final sortedMoods = List<MoodModel>.from(moods)..sort(
      (a, b) => (b.createdAt ?? DateTime.now()).compareTo(
        a.createdAt ?? DateTime.now(),
      ),
    );

    // Group by day
    final Map<String, List<MoodModel>> groupedMap = {};

    for (final mood in sortedMoods) {
      final date = mood.createdAt ?? DateTime.now();
      final dayKey = DateFormat('yyyy-MM-dd').format(date);

      if (!groupedMap.containsKey(dayKey)) {
        groupedMap[dayKey] = [];
      }
      groupedMap[dayKey]!.add(mood);
    }

    // Convert to list of DayMoodGroup objects
    return groupedMap.entries.map((entry) {
      final date = DateTime.parse(entry.key);
      return DayMoodGroup(date: date, moods: entry.value);
    }).toList();
  }
}

class DayMoodGroup {
  final DateTime date;
  final List<MoodModel> moods;

  DayMoodGroup({required this.date, required this.moods});
}

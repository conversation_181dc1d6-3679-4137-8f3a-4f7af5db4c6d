import 'package:watch_it/watch_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../models/activities_model.dart';
import '../objectbox.g.dart';

List<String> activities = [
  "Amusement park",
  "Antiquing",
  "Arcade",
  "Backpacking",
  "Cleaning",
  "Concert",
  "Dance club",
  "Dating",
  "Eating",
  "Exercise",
  "Family",
  "Friends",
  "Gaming",
  "Movies",
  "Read",
  "Relax",
  "Shopping",
  "Sport",
];

Future<void> loadActivitiesOnce() async {
  final prefs = await SharedPreferences.getInstance();
  final hasSeeded = prefs.getBool('activities_seeded') ?? false;
  
  if (!hasSeeded) {
    final box = di<Store>().box<ActivitiesModel>();
    
    try {
      for (var activity in activities) {
        box.put(ActivitiesModel(activityName: activity));
      }
      await prefs.setBool('activities_seeded', true);
    } catch (e) {
      // Handle error - maybe log it
      debugPrint('Failed to seed activities: $e');
    }
  }
}

{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:9000364630673312956", "lastPropertyId": "7:476437399537723634", "name": "MoodModel", "properties": [{"id": "1:4586764448619901744", "name": "id", "type": 6, "flags": 1}, {"id": "2:9215844035464463252", "name": "rating", "type": 8}, {"id": "3:886823196631613259", "name": "createdAt", "type": 10}, {"id": "4:2472737900249217245", "name": "mood", "type": 9}, {"id": "5:2486028341136845551", "name": "isSharedWithAI", "type": 1}, {"id": "6:1236734736256767476", "name": "note", "type": 9}, {"id": "7:476437399537723634", "name": "activity", "type": 9}], "relations": []}, {"id": "2:1270810904858355715", "lastPropertyId": "5:7217687718156760149", "name": "JournalModel", "properties": [{"id": "1:9138581968482764788", "name": "id", "type": 6, "flags": 1}, {"id": "2:4466694816372378779", "name": "note", "type": 9}, {"id": "3:8781107326323211266", "name": "createdAt", "type": 10}, {"id": "4:5854871498611686338", "name": "isSharedWithAI", "type": 1}, {"id": "5:7217687718156760149", "name": "imagePath", "type": 9}], "relations": []}, {"id": "3:2067554371463491883", "lastPropertyId": "2:8821550101412439556", "name": "ActivitiesModel", "properties": [{"id": "1:245324690174407564", "name": "id", "type": 6, "flags": 1}, {"id": "2:8821550101412439556", "name": "activityName", "type": 9}], "relations": []}], "lastEntityId": "3:2067554371463491883", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:moodvibe/models/audio_model.dart';
import 'package:watch_it/watch_it.dart';

import '../models/self_help_tips.dart';
import '../repositories/i_audio_server_api.dart';
import '../repositories/i_backend_api.dart';
import '../utilities/snack_bar_show.dart';

class SelfHelpTipsController extends ChangeNotifier {
  SelfHelpTipsController() {
    init();
  }

  List<SelfHelpTips> _selfHelpTips = [];

  List<SelfHelpTips> get selfHelpTips => _selfHelpTips;

  bool get isLoading => _isLoading;
  bool _isLoading = false;

  List<AudioModel> _selfHelpTipsAudio = [];

  List<AudioModel> get selfHelpTipsAudio => _selfHelpTipsAudio;

  List<AudioModel> _peacefulSoundsAudio = [];

  List<AudioModel> get peacefulSoundsAudio => _peacefulSoundsAudio;

  Future<void> init() async {
    //await getSelfHelpTips();
    await getSelfHelpTipsAudio();
    await getPeacefulSoundsAudio();
  }

  Future<void> getSelfHelpTips() async {
    _isLoading = true;
    notifyListeners();

    // Fetch self-help tips from API or local data source
    try {
      final response = await di<IBackendApi>().getSelfHelpTips();

      if (response.statusCode == 200) {
        // Extract the 'data' field from the response which contains the list
        final responseData = response.data as Map<String, dynamic>;
        final tipsData = responseData['data'] as List;

        debugPrint('✅ Successfully loaded ${tipsData.length} self-help tips');

        _selfHelpTips =
            tipsData
                .map(
                  (json) => SelfHelpTips.fromJson(json as Map<String, dynamic>),
                )
                .toList();
      } else {
        // Non-200 status code - show user-friendly error
        debugPrint(
          '❌ Failed to load self-help tips: Status ${response.statusCode}',
        );
        _selfHelpTips = [];
        di<SnackBarShow>().showSnackBar(
          "Can't load self-help tips",
          Colors.red,
        );
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();

      // Enhanced error logging with DioException details
      if (e is DioException) {
        debugPrint('🔍 DioException details for self-help tips:');
        debugPrint('   Status Code: ${e.response?.statusCode}');
        debugPrint('   Response Data: ${e.response?.data}');
        debugPrint('   Request URL: ${e.requestOptions.uri}');
        debugPrint('   Error Type: ${e.type}');
        debugPrint('   Error Message: ${e.message}');

        // Show user-friendly error message
        di<SnackBarShow>().showSnackBar(
          "Can't load self-help tips",
          Colors.red,
        );
      } else {
        debugPrint('❌ Unexpected error fetching self-help tips: $e');
        di<SnackBarShow>().showSnackBar(
          "Can't load self-help tips",
          Colors.red,
        );
      }
    }
  }

  Future<void> getSelfHelpTipsAudio() async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await di<IAudioServerApi>().getAudioUrl('meditations');

      // Handle different HTTP status codes
      if (response.statusCode == 200) {
        // Success - process the audio data
        if (response.data != null && response.data is List) {
          _selfHelpTipsAudio =
              (response.data as List)
                  .map(
                    (json) => AudioModel.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();
          debugPrint(
            '✅ Successfully loaded ${_selfHelpTipsAudio.length} meditation audios',
          );
        } else {
          debugPrint(
            '⚠️ Warning: Empty or invalid meditation audio data received',
          );
          _selfHelpTipsAudio = [];
        }
      } else {
        // Non-200 status code - show user-friendly error
        debugPrint(
          '❌ Failed to load meditation audio: Status ${response.statusCode}',
        );
        _selfHelpTipsAudio = [];
        di<SnackBarShow>().showSnackBar(
          "Can't load meditation audio",
          Colors.red,
        );
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();

      // Enhanced error logging with DioException details
      if (e is DioException) {
        debugPrint('🔍 DioException details for meditations:');
        debugPrint('   Status Code: ${e.response?.statusCode}');
        debugPrint('   Response Data: ${e.response?.data}');
        debugPrint('   Request URL: ${e.requestOptions.uri}');
        debugPrint('   Headers: ${e.requestOptions.headers}');
        debugPrint('   Error Type: ${e.type}');
        debugPrint('   Error Message: ${e.message}');

        // Handle network-specific errors
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
            debugPrint('❌ Connection timeout: Server took too long to respond');
            throw Exception(
              'Connection timeout: Please check your internet connection',
            );

          case DioExceptionType.sendTimeout:
            debugPrint('❌ Send timeout: Request took too long to send');
            throw Exception('Request timeout: Please try again');

          case DioExceptionType.receiveTimeout:
            debugPrint('❌ Receive timeout: Server response took too long');
            throw Exception('Response timeout: Please try again');

          case DioExceptionType.connectionError:
            debugPrint('❌ Connection error: Unable to connect to server');
            throw Exception(
              'Connection error: Please check your internet connection',
            );

          case DioExceptionType.cancel:
            debugPrint('❌ Request cancelled');
            throw Exception('Request was cancelled');

          default:
            debugPrint('❌ Network error: ${e.message}');
            throw Exception('Network error: Unable to fetch meditation files');
        }
      } else {
        debugPrint('❌ Unexpected error fetching meditation audio: $e');
        throw Exception('Failed to load meditation files: $e');
      }
    }
  }

  Future<void> getPeacefulSoundsAudio() async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await di<IAudioServerApi>().getAudioUrl('sounds');

      // Handle different HTTP status codes
      if (response.statusCode == 200) {
        // Success - process the audio data
        if (response.data != null && response.data is List) {
          _peacefulSoundsAudio =
              (response.data as List)
                  .map(
                    (json) => AudioModel.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();
          debugPrint(
            '✅ Successfully loaded ${_peacefulSoundsAudio.length} peaceful sounds',
          );
        } else {
          debugPrint('⚠️ Warning: Empty or invalid audio data received');
          _peacefulSoundsAudio = [];
        }
      } else {
        // Non-200 status code - show user-friendly error
        debugPrint(
          '❌ Failed to load peaceful sounds: Status ${response.statusCode}',
        );
        _peacefulSoundsAudio = [];
        di<SnackBarShow>().showSnackBar("Can't load audio", Colors.red);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();

      // Enhanced error logging with DioException details
      if (e is DioException) {
        debugPrint('🔍 DioException details:');
        debugPrint('   Status Code: ${e.response?.statusCode}');
        debugPrint('   Response Data: ${e.response?.data}');
        debugPrint('   Request URL: ${e.requestOptions.uri}');
        debugPrint('   Headers: ${e.requestOptions.headers}');
        debugPrint('   Error Type: ${e.type}');
        debugPrint('   Error Message: ${e.message}');

        // Handle network-specific errors
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
            debugPrint('❌ Connection timeout: Server took too long to respond');
            throw Exception(
              'Connection timeout: Please check your internet connection',
            );

          case DioExceptionType.sendTimeout:
            debugPrint('❌ Send timeout: Request took too long to send');
            throw Exception('Request timeout: Please try again');

          case DioExceptionType.receiveTimeout:
            debugPrint('❌ Receive timeout: Server response took too long');
            throw Exception('Response timeout: Please try again');

          case DioExceptionType.connectionError:
            debugPrint('❌ Connection error: Unable to connect to server');
            throw Exception(
              'Connection error: Please check your internet connection',
            );

          case DioExceptionType.cancel:
            debugPrint('❌ Request cancelled');
            throw Exception('Request was cancelled');

          default:
            debugPrint('❌ Network error: ${e.message}');
            throw Exception('Network error: Unable to fetch audio files');
        }
      } else {
        debugPrint('❌ Unexpected error fetching peaceful sounds audio: $e');
        throw Exception('Failed to load audio files: $e');
      }
    }
  }
}

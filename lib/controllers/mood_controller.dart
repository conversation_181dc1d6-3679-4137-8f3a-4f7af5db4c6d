import 'package:flutter/cupertino.dart';
import 'package:moodvibe/controllers/auth_controller.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:watch_it/watch_it.dart';

import '../models/activities_model.dart';
import '../models/mood.dart';
import '../repositories/i_add_activities_local_db.dart';
import '../repositories/i_add_mood_local_db.dart';

import '../repositories/i_backend_api.dart';
import '../services/analytics_service.dart';
import '../utilities/snack_bar_show.dart';

class MoodController extends ChangeNotifier {
  MoodController() {
    init();
  }

  List<MoodModel> _moods = [];

  List<MoodModel> get moods => _moods;

  List<MoodModel> _calenderMoods = [];

  List<MoodModel> get calenderMoods => _calenderMoods;

  double rating = 0;
  String mood = 'Mad';

  bool get isLoading => _isLoading;
  bool _isLoading = false;

  List<ActivitiesModel> _activities = [];

  List<ActivitiesModel> get activities => _activities;

  Future<void> init() async {
    await getMoods();
    await sortMoodsByDate(DateTime.now());
    await getActivities();
  }

  Future<void> getActivities() async {
    _activities = await di<IAddActivitiesLocalDb>().getActivities();
  }

  Future<void> getMoods() async {
    _isLoading = true;
    notifyListeners();
    _moods = await di<IAddMoodLocalDb>().getMoods();
    _isLoading = false;
    notifyListeners();
  }

  Future<void> addMood(
    String note,
    bool isSharedWithAI,
    double rating, {
    DateTime? selectedDate,
    String? selectedActivity,
  }) async {
    if (rating == 1) {
      mood = 'Mad';
    } else if (rating == 2) {
      mood = 'Bad';
    } else if (rating == 3) {
      mood = 'OK';
    } else if (rating == 4) {
      mood = 'Happy';
    } else if (rating == 5) {
      mood = 'Joy';
    }

    debugPrint(note);
    debugPrint(isSharedWithAI.toString());
    debugPrint(rating.toString());
    debugPrint(mood);

    await di<IAddMoodLocalDb>().saveMood(
      MoodModel(
        rating: rating,
        createdAt: selectedDate ?? DateTime.now(),
        mood: mood,
        isSharedWithAI: isSharedWithAI,
        note: note,
        activity: selectedActivity,
      ),
    );

    await di<AnalyticsService>().logEvent(
      name: 'add_mood',
      parameters: {'mood': mood, 'rating': rating},
    );

    di<SnackBarShow>().showSnackBar(
      'Mood saved successfully!',
      AppTheme.blueberry,
    );

    if (isSharedWithAI) {
      // Get current user ID from auth
      final currentUserId = di<AuthController>().getCurrentUserId();

      debugPrint('Current user ID: $currentUserId');

      if (currentUserId != null) {
        try {
          await di<IBackendApi>().addMood(
            note,
            isSharedWithAI,
            rating,
            mood,
            currentUserId,
          );
          debugPrint('Mood successfully shared with AI backend');
        } catch (e) {
          debugPrint('Failed to share mood with AI backend: $e');
          di<SnackBarShow>().showSnackBar(
            'Mood saved locally, but failed to sync with AI. Please check your connection.',
            AppTheme.apricot,
          );
        }
      } else {
        debugPrint('Warning: User not logged in, cannot share mood with AI');
        di<SnackBarShow>().showSnackBar(
          'Please log in to share moods with AI',
          AppTheme.apricot,
        );
      }
    }

    // Refresh the moods list after adding
    await sortMoodsByDate(selectedDate ?? DateTime.now());
    await getMoods();
  }

  Future<void> deleteMood(int id) async {
    await di<IAddMoodLocalDb>().deleteMood(id);

    // Refresh the moods list after deleting
    await getMoods();
  }

  Future<void> sortMoodsByDate(DateTime date) async {
    _calenderMoods = await di<IAddMoodLocalDb>().getMoods();
    _calenderMoods =
        _calenderMoods.where((mood) {
          final moodDate = mood.createdAt ?? DateTime.now();
          return moodDate.year == date.year &&
              moodDate.month == date.month &&
              moodDate.day == date.day;
        }).toList();

    notifyListeners();
  }

  String? _selectedMoodFilter;
  String? _selectedActivityFilter;

  String? get selectedMoodFilter => _selectedMoodFilter;
  String? get selectedActivityFilter => _selectedActivityFilter;

  Future<void> sortFilterMoodsByMoodOrActivity({
    String? moodFilter,
    String? activityFilter,
  }) async {
    _selectedMoodFilter = moodFilter;
    _selectedActivityFilter = activityFilter;

    // Get all moods first
    List<MoodModel> filteredMoods = await di<IAddMoodLocalDb>().getMoods();

    // Apply mood filter if selected
    if (moodFilter != null && moodFilter.isNotEmpty) {
      filteredMoods =
          filteredMoods.where((mood) => mood.mood == moodFilter).toList();
    }

    // Apply activity filter if selected
    if (activityFilter != null && activityFilter.isNotEmpty) {
      filteredMoods =
          filteredMoods
              .where((mood) => mood.activity == activityFilter)
              .toList();
    }

    _calenderMoods = filteredMoods;
    notifyListeners();
  }

  Future<void> clearFilters() async {
    _selectedMoodFilter = null;
    _selectedActivityFilter = null;
    await sortMoodsByDate(DateTime.now());
  }
}
